"use client";

import { Suspense, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Mail, Lock, Eye, EyeOff, User, Globe, Loader2 } from "lucide-react";
import { FcGoogle } from "react-icons/fc";
import {
  signInWithEmailAndPassword,
  GoogleAuthProvider,
  signInWithPopup,
} from "firebase/auth";
import { auth } from "@/lib/firebase";
import Link from "next/link";

function SignInContent() {
  const searchParams = useSearchParams();
  const redirect = searchParams?.get("redirect") || "/dashboard";
  const role = searchParams?.get("role") as "user" | "brand" | "agent" | null;

  return <SignInForm redirect={redirect} role={role} />;
}

export default function SignInPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      }
    >
      <SignInContent />
    </Suspense>
  );
}

function SignInForm({
  redirect,
  role,
}: {
  redirect: string;
  role: "user" | "brand" | "agent" | null;
}) {
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const [name, setName] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [website, setWebsite] = useState("");
  const [emailUsername, setEmailUsername] = useState("");
  const [websiteDomain, setWebsiteDomain] = useState("");
  const [selectedRole, setSelectedRole] = useState<"user" | "brand" | "agent">(
    (role as "user" | "brand" | "agent") || "user"
  );

  const googleProvider = new GoogleAuthProvider();

  const handleSuccess = () => {
    router.push(redirect);
  };

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      await signInWithEmailAndPassword(auth, email, password);
      handleSuccess();
    } catch (err) {
      setError((err as Error).message);
      toast.error("Invalid email or password");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError("");

    try {
      await signInWithPopup(auth, googleProvider);
      handleSuccess();
    } catch (err) {
      setError((err as Error).message);
      toast.error("Failed to sign in with Google");
    } finally {
      setIsLoading(false);
    }
  };

  const extractDomainFromWebsite = (websiteUrl: string): string => {
    try {
      let url = websiteUrl.trim();
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        url = "https://" + url;
      }
      const domain = new URL(url).hostname.toLowerCase();
      return domain.startsWith("www.") ? domain.substring(4) : domain;
    } catch {
      return "";
    }
  };

  const handleWebsiteChange = (value: string) => {
    setWebsite(value);
    const domain = extractDomainFromWebsite(value);
    setWebsiteDomain(domain);
    if (emailUsername && domain) {
      setEmail(`${emailUsername}@${domain}`);
    }
  };

  const handleEmailUsernameChange = (value: string) => {
    setEmailUsername(value);
    if (value && websiteDomain) {
      setEmail(`${value}@${websiteDomain}`);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Simple Back Button */}
      <div className="absolute top-4 left-4">
        <button
          onClick={handleBack}
          className="p-2 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          aria-label="Go back"
        >
          <svg
            className="h-5 w-5 text-gray-700 dark:text-gray-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
      </div>

      <div className="flex flex-1">
        {/* Left side - Image */}
        <div className="hidden lg:flex w-1/2 bg-black items-center justify-center p-12">
          <div className="max-w-md text-center">
            <h2 className="text-4xl font-bold mb-6 text-white">
              Welcome to AdMesh
            </h2>
            <p className="text-lg mb-8 text-blue-100">
              {isSignUp
                ? "Join our platform to connect with customers through AI agents"
                : "Sign in to access your account and manage your offers"}
            </p>
            <div className="mt-8 bg-black rounded-lg p-6 text-left text-white border border-gray-800">
              {selectedRole === "brand" && (
                <>
                  <div className="mb-4">
                    <p className="italic text-white">
                      &quot;AdMesh gave us instant visibility across AI apps. We
                      hit 500 conversions in our first week through agent-driven
                      offers. It&apos;s the future of marketing.&quot;
                    </p>
                  </div>
                  <div className="text-sm">
                    <p className="font-semibold text-white">Samantha G.</p>
                    <p className="text-gray-300">Head of Growth, Blinko CRM</p>
                  </div>
                </>
              )}

              {selectedRole === "agent" && (
                <>
                  <div className="mb-4">
                    <p className="italic text-white">
                      &quot;The quality of leads from AdMesh is unmatched.
                      We&apos;ve seen a 300% increase in qualified demos since
                      integrating our offers. Highly recommended!&quot;
                    </p>
                  </div>
                  <div className="text-sm">
                    <p className="font-semibold text-white">Kai T.</p>
                    <p className="text-gray-300">Creator of AskAIAgent</p>
                  </div>
                </>
              )}

              {selectedRole === "user" && (
                <>
                  <div className="mb-4">
                    <p className="italic text-white">
                      &quot;AdMesh transformed our outreach. We can now connect
                      with our ideal customers in a way that feels natural and
                      valuable. It&apos;s a game-changer.&quot;
                    </p>
                  </div>
                  <div className="text-sm">
                    <p className="font-semibold text-white">Elena M.</p>
                    <p className="text-gray-300">Content Creator</p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Right side - Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md space-y-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {isSignUp ? "Create an account" : "Welcome back"}
              </h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {isSignUp
                  ? "Enter your details to create an account"
                  : "Sign in to your account to continue"}
              </p>
            </div>

            {isSignUp && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  I am a...
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {[
                    { id: "user", label: "User" },
                    { id: "brand", label: "Brand" },
                    { id: "agent", label: "Agent" },
                  ].map((role) => (
                    <button
                      key={role.id}
                      type="button"
                      onClick={() =>
                        setSelectedRole(role.id as "user" | "brand" | "agent")
                      }
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        selectedRole === role.id
                          ? "bg-blue-600 text-white"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                      }`}
                    >
                      {role.label}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {!isSignUp ? (
              // Sign In Form
              <form onSubmit={handleEmailSignIn} className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      Email
                    </label>
                    <div className="relative">
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10 h-12"
                        required
                      />
                      <Mail className="absolute left-3 top-3.5 text-gray-400" />
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <label
                        htmlFor="password"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Password
                      </label>
                      <Link
                        href="/auth/forgot-password"
                        className="text-sm text-primary hover:text-primary/80 hover:underline"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="••••••••"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="pl-10 h-12 pr-10"
                        required
                      />
                      <Lock className="absolute left-3 top-3.5 text-gray-400" />
                      <button
                        type="button"
                        className="absolute right-3 top-3.5 text-gray-400 hover:text-gray-500"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff
                            className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 cursor-pointer text-muted-foreground"
                            onClick={() => setShowPassword(false)}
                          />
                        ) : (
                          <Eye
                            className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 cursor-pointer text-muted-foreground"
                            onClick={() => setShowPassword(true)}
                          />
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg p-3 text-sm">
                    {error}
                  </div>
                )}

                <Button
                  type="submit"
                  className="w-full h-12"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    "Sign in"
                  )}
                </Button>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-400">
                      Or continue with
                    </span>
                  </div>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full h-12 flex items-center justify-center gap-2"
                  onClick={handleGoogleSignIn}
                  disabled={isLoading}
                >
                  <FcGoogle className="h-5 w-5" />
                  <span>Google</span>
                </Button>

                <p className="mt-2 text-xs text-gray-500 text-center dark:text-gray-400">
                  By continuing, you agree to AdMesh&apos;s{" "}
                  <Link href="/terms" className="underline hover:text-primary">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link
                    href="/privacy"
                    className="underline hover:text-primary"
                  >
                    Privacy Policy
                  </Link>
                  , and to receive periodic emails with updates.
                </p>
              </form>
            ) : (
              // Sign Up Form
              <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
                {selectedRole === "brand" ? (
                  <>
                    <div>
                      <label
                        htmlFor="company-name"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                      >
                        Company Name
                      </label>
                      <div className="relative">
                        <Input
                          id="company-name"
                          placeholder="Acme Inc."
                          value={companyName}
                          onChange={(e) => setCompanyName(e.target.value)}
                          className="pl-10 h-12"
                          required
                        />
                        <User className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="website"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                      >
                        Company Website
                      </label>
                      <div className="relative">
                        <Input
                          id="website"
                          type="url"
                          placeholder="https://example.com"
                          value={website}
                          onChange={(e) => handleWebsiteChange(e.target.value)}
                          className="pl-10 h-12"
                          required
                        />
                        <Globe className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      </div>
                    </div>

                    {websiteDomain && (
                      <div>
                        <label
                          htmlFor="email-username"
                          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                        >
                          Work Email
                        </label>
                        <div className="flex">
                          <div className="relative flex-1">
                            <Input
                              id="email-username"
                              placeholder="username"
                              value={emailUsername}
                              onChange={(e) =>
                                handleEmailUsernameChange(e.target.value)
                              }
                              className="rounded-r-none h-12"
                              required
                            />
                            <span className="absolute right-3 top-3.5 text-gray-500">
                              @{websiteDomain}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      {selectedRole === "agent"
                        ? "Agent/Company Name"
                        : "Full Name"}
                    </label>
                    <div className="relative">
                      <Input
                        id="name"
                        placeholder={
                          selectedRole === "agent" ? "My AI Agent" : "John Doe"
                        }
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="pl-10 h-12"
                        required
                      />
                      <User className="absolute left-3 top-3.5 text-gray-400" />
                    </div>
                  </div>
                )}

                {selectedRole !== "brand" && (
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      Email
                    </label>
                    <div className="relative">
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10 h-12"
                        required
                      />
                      <Mail className="absolute left-3 top-3.5 text-gray-400" />
                    </div>
                  </div>
                )}

                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="pl-10 h-12 pr-10"
                      required
                    />
                    <Lock className="absolute left-3 top-3.5 text-gray-400" />
                    <button
                      type="button"
                      className="absolute right-3 top-3.5 text-gray-400 hover:text-gray-500"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff /> : <Eye />}
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating account...
                    </>
                  ) : (
                    "Create account"
                  )}
                </Button>

                <p className="mt-2 text-xs text-gray-500 text-center dark:text-gray-400">
                  By continuing, you agree to AdMesh&apos;s{" "}
                  <Link href="/terms" className="underline hover:text-primary">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link
                    href="/privacy"
                    className="underline hover:text-primary"
                  >
                    Privacy Policy
                  </Link>
                  , and to receive periodic emails with updates.
                </p>
              </form>
            )}

            <p className="text-center text-sm text-gray-600 dark:text-gray-400">
              {isSignUp ? "Already have an account?" : "Don't have an account?"}{" "}
              <button
                type="button"
                onClick={() => setIsSignUp(!isSignUp)}
                className="text-blue-600 hover:text-blue-500 hover:underline font-medium"
              >
                {isSignUp ? "Sign in" : "Sign up"}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
